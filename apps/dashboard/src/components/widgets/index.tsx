import { useUpdateWidget } from "@/routes/mini-apps/mini-app-editor-design/hooks/use-widgets"
import { Widget } from "@/routes/mini-apps/mini-app-editor-design/stores/mini-app-design-store"
import { components, operations } from "@saf/sdk"
import { toast } from "@saf/ui"
import { useCallback } from "react"
import { useDebouncedCallback } from "use-debounce"
// import { ButtonEditor, ButtonWidget } from "./button"
import { CollectionEditor, CollectionWidget } from "./collection"
import { TextEditor, TextWidget } from "./text"
import { TextEntryEditor, TextEntryWidget } from "./text-entry"
import { TitleEditor, TitleWidget } from "./title"
import { cn } from "@/lib/utils"
import { SeparatorEditor, SeparatorWidget } from "./separator"
import { ImageEditor, ImageWidget } from "./image"
import { FormContainerEditor, FormContainerWidget } from "./form_container"

export * from "./button"
export * from "./collection"
export * from "./text"
export * from "./text-entry"
export * from "./title"
export * from "./separator"
export * from "./image"
export * from "./form_container"

type UpdateRequestBody = operations["MiniAppWidget_update"]["requestBody"]["content"]["application/json"]

export const WidgetEditor = ({
  teamId,
  miniAppId,
  versionId,
  pageId,
  widget,
}: {
  teamId: string
  miniAppId: string
  versionId: string
  pageId: string
  widget: Widget
}) => {
  const updateWidget = useUpdateWidget()

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedOnUpdate = useCallback(
    useDebouncedCallback((values: UpdateRequestBody) => {
      updateWidget.mutate(
        {
          body: values,
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: parseInt(miniAppId),
              versionId: parseInt(versionId),
              pageId: parseInt(pageId),
              widgetId: widget.id,
            },
          },
        },
        {
          onError: (error) => {
            toast.error(error.message || "Failed to update widget")
          },
        },
      )
    }, 500),
    [teamId, miniAppId, versionId, pageId, widget.id],
  )

  switch (widget.widgetType) {
    case "collection":
      return (
        <CollectionEditor data={widget as components["schemas"]["CollectionWidget"]} onUpdate={debouncedOnUpdate} />
      )
    case "text":
      return <TextEditor data={widget as components["schemas"]["TextWidget"]} onUpdate={debouncedOnUpdate} />
    case "title":
      return <TitleEditor data={widget as components["schemas"]["TitleWidget"]} onUpdate={debouncedOnUpdate} />
    // case "button":
    //   return <ButtonEditor data={widget as components["schemas"]["ButtonWidget"]} />
    case "text_entry":
      return <TextEntryEditor data={widget as components["schemas"]["TextEntryWidget"]} />
    case "separator":
      return <SeparatorEditor data={widget as components["schemas"]["SeparatorWidget"]} onUpdate={debouncedOnUpdate} />
    case "image":
      return <ImageEditor data={widget as components["schemas"]["ImageWidget"]} onUpdate={debouncedOnUpdate} />
    case "form_container":
      return (
        <FormContainerEditor
          data={widget as components["schemas"]["FormContainerWidget"]}
          onUpdate={debouncedOnUpdate}
        />
      )
    default:
      return <div>{widget.widgetType}</div>
  }
}

export const renderWidget = (
  widget: Widget,
  focusedWidgetId?: number,
  onWidgetChanged?: (widgetId: number) => void,
) => {
  let widgetToReturn: React.ReactNode

  switch (widget.widgetType) {
    case "collection":
      widgetToReturn = <CollectionWidget data={widget as components["schemas"]["CollectionWidget"]} />
      break
    case "text":
      widgetToReturn = <TextWidget data={widget as components["schemas"]["TextWidget"]} />
      break
    case "title":
      widgetToReturn = <TitleWidget data={widget as components["schemas"]["TitleWidget"]} />
      break
    // case "button":
    //   widgetToReturn = <ButtonWidget data={widget as components["schemas"]["ButtonWidget"]} />
    //   break
    case "text_entry":
      widgetToReturn = <TextEntryWidget data={widget as components["schemas"]["TextEntryWidget"]} />
      break
    case "separator":
      widgetToReturn = <SeparatorWidget data={widget as components["schemas"]["SeparatorWidget"]} />
      break
    case "image":
      widgetToReturn = <ImageWidget data={widget as components["schemas"]["ImageWidget"]} />
      break
    case "form_container":
      widgetToReturn = <FormContainerWidget data={widget as components["schemas"]["FormContainerWidget"]} />
      break
    default:
      widgetToReturn = <div className="mx-2 my-1 rounded border bg-ui-bg-base p-4">{widget.widgetType}</div>
      break
  }

  return (
    <div key={widget.id} onClick={() => onWidgetChanged?.(widget.id)} className="relative">
      <div
        className={cn(
          widget.id === focusedWidgetId && "pointer-events-none absolute inset-0 border-2 border-ui-bg-interactive",
        )}
      />
      {widgetToReturn}
    </div>
  )
}
